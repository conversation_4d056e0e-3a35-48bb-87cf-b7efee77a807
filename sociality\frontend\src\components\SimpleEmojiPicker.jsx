import React from 'react';
import {
  Box,
  Button,
  Text,
  Flex,
  Grid,
  VStack,
  useColorModeValue
} from '@chakra-ui/react';

const SimpleEmojiPicker = ({ onEmojiClick, onClose }) => {
  const bgColor = useColorModeValue("white", "#2d2d2d");
  const textColor = useColorModeValue("gray.800", "white");
  const borderColor = useColorModeValue("gray.200", "gray.600");

  // Comprehensive emoji categories
  const emojiCategories = {
    'Smileys & People': [
      '😀', '😃', '😄', '😁', '😆', '😅', '🤣', '😂', '🙂', '🙃',
      '😉', '😊', '😇', '🥰', '😍', '🤩', '😘', '😗', '😚', '😙',
      '😋', '😛', '😜', '🤪', '😝', '🤑', '🤗', '🤭', '🤫', '🤔',
      '🤐', '🤨', '😐', '😑', '😶', '😏', '😒', '🙄', '😬', '🤥',
      '😔', '😪', '🤤', '😴', '😷', '🤒', '🤕', '🤢', '🤮', '🤧',
      '🥵', '🥶', '🥴', '😵', '🤯', '🤠', '🥳', '😎', '🤓', '🧐'
    ],
    'Animals & Nature': [
      '🐶', '🐱', '🐭', '🐹', '🐰', '🦊', '🐻', '🐼', '🐨', '🐯',
      '🦁', '🐮', '🐷', '🐽', '🐸', '🐵', '🙈', '🙉', '🙊', '🐒',
      '🐔', '🐧', '🐦', '🐤', '🐣', '🐥', '🦆', '🦅', '🦉', '🦇',
      '🐺', '🐗', '🐴', '🦄', '🐝', '🐛', '🦋', '🐌', '🐞', '🐜'
    ],
    'Food & Drink': [
      '🍎', '🍐', '🍊', '🍋', '🍌', '🍉', '🍇', '🍓', '🍈', '🍒',
      '🍑', '🥭', '🍍', '🥥', '🥝', '🍅', '🍆', '🥑', '🥦', '🥬',
      '🥒', '🌶️', '🌽', '🥕', '🧄', '🧅', '🥔', '🍠', '🥐', '🍞',
      '🥖', '🥨', '🧀', '🥚', '🍳', '🧈', '🥞', '🧇', '🥓', '🥩'
    ],
    'Activities': [
      '⚽', '🏀', '🏈', '⚾', '🥎', '🎾', '🏐', '🏉', '🥏', '🎱',
      '🪀', '🏓', '🏸', '🏒', '🏑', '🥍', '🏏', '🪃', '🥅', '⛳',
      '🪁', '🏹', '🎣', '🤿', '🥊', '🥋', '🎽', '🛹', '🛷', '⛸️'
    ],
    'Travel & Places': [
      '🚗', '🚕', '🚙', '🚌', '🚎', '🏎️', '🚓', '🚑', '🚒', '🚐',
      '🛻', '🚚', '🚛', '🚜', '🏍️', '🛵', '🚲', '🛴', '🛺', '🚨',
      '🚔', '🚍', '🚘', '🚖', '🚡', '🚠', '🚟', '🚃', '🚋', '🚞'
    ],
    'Objects': [
      '⌚', '📱', '📲', '💻', '⌨️', '🖥️', '🖨️', '🖱️', '🖲️', '🕹️',
      '🗜️', '💽', '💾', '💿', '📀', '📼', '📷', '📸', '📹', '🎥',
      '📽️', '🎞️', '📞', '☎️', '📟', '📠', '📺', '📻', '🎙️', '🎚️'
    ],
    'Symbols': [
      '❤️', '🧡', '💛', '💚', '💙', '💜', '🖤', '🤍', '🤎', '💔',
      '❣️', '💕', '💞', '💓', '💗', '💖', '💘', '💝', '💟', '☮️',
      '✝️', '☪️', '🕉️', '☸️', '✡️', '🔯', '🕎', '☯️', '☦️', '🛐'
    ],
    'Flags': [
      '🏁', '🚩', '🎌', '🏴', '🏳️', '🏳️‍🌈', '🏳️‍⚧️', '🏴‍☠️', '🇦🇫', '🇦🇽',
      '🇦🇱', '🇩🇿', '🇦🇸', '🇦🇩', '🇦🇴', '🇦🇮', '🇦🇶', '🇦🇬', '🇦🇷', '🇦🇲'
    ]
  };

  const handleEmojiClick = (emoji) => {
    onEmojiClick({ emoji });
  };

  return (
    <VStack spacing={0} align="stretch" h="100%">
      {/* Header */}
      <Box
        bg={useColorModeValue("gray.50", "#1a1a1a")}
        px={3}
        py={2}
        borderBottom="1px solid"
        borderColor={borderColor}
      >
        <Text fontSize="sm" fontWeight="medium" color={textColor}>
          Choose an emoji
        </Text>
        <Text fontSize="xs" color={useColorModeValue("gray.500", "gray.400")} mt={1}>
          Click to add • Ctrl+Click to send
        </Text>
      </Box>

      {/* Emoji Grid */}
      <Box flex="1" overflowY="auto" p={3}>
        <VStack spacing={4} align="stretch">
          {Object.entries(emojiCategories).map(([category, emojis]) => (
            <Box key={category}>
              <Text 
                fontSize="xs" 
                fontWeight="bold" 
                color={useColorModeValue("gray.600", "gray.400")} 
                mb={2}
                textTransform="uppercase"
                letterSpacing="wide"
              >
                {category}
              </Text>
              <Grid templateColumns="repeat(8, 1fr)" gap={1}>
                {emojis.map((emoji, index) => (
                  <Button
                    key={`${category}-${index}`}
                    variant="ghost"
                    size="sm"
                    fontSize="lg"
                    onClick={() => handleEmojiClick(emoji)}
                    _hover={{ 
                      bg: useColorModeValue("gray.100", "rgba(255, 255, 255, 0.1)"),
                      transform: "scale(1.1)"
                    }}
                    borderRadius="md"
                    p={1}
                    minW="auto"
                    h="auto"
                    transition="all 0.2s"
                  >
                    {emoji}
                  </Button>
                ))}
              </Grid>
            </Box>
          ))}
        </VStack>
      </Box>
    </VStack>
  );
};

export default SimpleEmojiPicker;
