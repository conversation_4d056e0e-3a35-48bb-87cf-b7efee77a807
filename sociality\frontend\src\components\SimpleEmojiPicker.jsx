import React from 'react';
import {
  Box,
  Button,
  Text,
  Flex,
  Grid,
  VStack,
  useColorModeValue
} from '@chakra-ui/react';

const SimpleEmojiPicker = ({ onEmojiClick, onClose }) => {
  const bgColor = useColorModeValue("white", "#2d2d2d");
  const textColor = useColorModeValue("gray.800", "white");
  const borderColor = useColorModeValue("gray.200", "gray.600");

  // Comprehensive emoji categories
  const emojiCategories = {
    'Smileys & People': [
      '😀', '😃', '😄', '😁', '😆', '😅', '🤣', '😂', '🙂', '🙃',
      '😉', '😊', '😇', '🥰', '😍', '🤩', '😘', '😗', '😚', '😙',
      '😋', '😛', '😜', '🤪', '😝', '🤑', '🤗', '🤭', '🤫', '🤔',
      '🤐', '🤨', '😐', '😑', '😶', '😏', '😒', '🙄', '😬', '🤥',
      '😔', '😪', '🤤', '😴', '😷', '🤒', '🤕', '🤢', '🤮', '🤧',
      '🥵', '🥶', '🥴', '😵', '🤯', '🤠', '🥳', '😎', '🤓', '🧐'
    ],
    'Animals & Nature': [
      '🐶', '🐱', '🐭', '🐹', '🐰', '🦊', '🐻', '🐼', '🐨', '🐯',
      '🦁', '🐮', '🐷', '🐽', '🐸', '🐵', '🙈', '🙉', '🙊', '🐒',
      '🐔', '🐧', '🐦', '🐤', '🐣', '🐥', '🦆', '🦅', '🦉', '🦇',
      '🐺', '🐗', '🐴', '🦄', '🐝', '🐛', '🦋', '🐌', '🐞', '🐜'
    ],
    'Food & Drink': [
      '🍎', '🍐', '🍊', '🍋', '🍌', '🍉', '🍇', '🍓', '🍈', '🍒',
      '🍑', '🥭', '🍍', '🥥', '🥝', '🍅', '🍆', '🥑', '🥦', '🥬',
      '🥒', '🌶️', '🌽', '🥕', '🧄', '🧅', '🥔', '🍠', '🥐', '🍞',
      '🥖', '🥨', '🧀', '🥚', '🍳', '🧈', '🥞', '🧇', '🥓', '🥩'
    ],
    'Activities': [
      '⚽', '🏀', '🏈', '⚾', '🥎', '🎾', '🏐', '🏉', '🥏', '🎱',
      '🪀', '🏓', '🏸', '🏒', '🏑', '🥍', '🏏', '🪃', '🥅', '⛳',
      '🪁', '🏹', '🎣', '🤿', '🥊', '🥋', '🎽', '🛹', '🛷', '⛸️'
    ],
    'Travel & Places': [
      '🚗', '🚕', '🚙', '🚌', '🚎', '🏎️', '🚓', '🚑', '🚒', '🚐',
      '🛻', '🚚', '🚛', '🚜', '🏍️', '🛵', '🚲', '🛴', '🛺', '🚨',
      '🚔', '🚍', '🚘', '🚖', '🚡', '🚠', '🚟', '🚃', '🚋', '🚞'
    ],
    'Objects': [
      '⌚', '📱', '📲', '💻', '⌨️', '🖥️', '🖨️', '🖱️', '🖲️', '🕹️',
      '🗜️', '💽', '💾', '💿', '📀', '📼', '📷', '📸', '📹', '🎥',
      '📽️', '🎞️', '📞', '☎️', '📟', '📠', '📺', '📻', '🎙️', '🎚️'
    ],
    'Symbols': [
      '❤️', '🧡', '💛', '💚', '💙', '💜', '🖤', '🤍', '🤎', '💔',
      '❣️', '💕', '💞', '💓', '💗', '💖', '💘', '💝', '💟', '☮️',
      '✝️', '☪️', '🕉️', '☸️', '✡️', '🔯', '🕎', '☯️', '☦️', '🛐'
    ],
    'Flags': [
      '🏁', '🚩', '🎌', '🏴', '🏳️', '🏳️‍🌈', '🏳️‍⚧️', '🏴‍☠️', '🇦🇫', '🇦🇽',
      '🇦🇱', '🇩🇿', '🇦🇸', '🇦🇩', '🇦🇴', '🇦🇮', '🇦🇶', '🇦🇬', '🇦🇷', '🇦🇲'
    ]
  };

  const handleEmojiClick = (emoji) => {
    onEmojiClick({ emoji });
  };

  return (
    <VStack spacing={0} align="stretch" h="100%">
      {/* Header */}
      <Box
        bg={useColorModeValue("gray.50", "#1a1a1a")}
        px={3}
        py={2}
        borderBottom="1px solid"
        borderColor={borderColor}
      >
        <Text fontSize="xs" fontWeight="medium" color={textColor}>
          Choose an emoji
        </Text>
      </Box>

      {/* Emoji Grid */}
      <Box
        flex="1"
        overflowY="auto"
        overflowX="hidden"
        p={3}
        css={{
          '&::-webkit-scrollbar': {
            width: '6px',
          },
          '&::-webkit-scrollbar-track': {
            background: 'transparent',
          },
          '&::-webkit-scrollbar-thumb': {
            background: useColorModeValue('#CBD5E0', '#4A5568'),
            borderRadius: '3px',
          },
          '&::-webkit-scrollbar-thumb:hover': {
            background: useColorModeValue('#A0AEC0', '#718096'),
          },
        }}
      >
        <VStack spacing={3} align="stretch" w="100%">
          {Object.entries(emojiCategories).map(([category, emojis]) => (
            <Box key={category} w="100%">
              <Text
                fontSize="xs"
                fontWeight="bold"
                color={useColorModeValue("gray.600", "gray.400")}
                mb={1}
                textTransform="uppercase"
                letterSpacing="wide"
                position="sticky"
                top="0"
                bg={useColorModeValue("white", "#2d2d2d")}
                py={1}
                zIndex={1}
              >
                {category}
              </Text>
              <Grid templateColumns="repeat(7, 1fr)" gap={1} w="100%">
                {emojis.map((emoji, index) => (
                  <Button
                    key={`${category}-${index}`}
                    variant="ghost"
                    size="xs"
                    fontSize="md"
                    onClick={() => handleEmojiClick(emoji)}
                    _hover={{
                      bg: useColorModeValue("gray.100", "rgba(255, 255, 255, 0.1)"),
                      transform: "scale(1.1)"
                    }}
                    _active={{
                      bg: useColorModeValue("gray.200", "rgba(255, 255, 255, 0.2)"),
                      transform: "scale(0.95)"
                    }}
                    borderRadius="md"
                    p={1}
                    w="32px"
                    h="32px"
                    minW="32px"
                    maxW="32px"
                    transition="all 0.15s"
                    cursor="pointer"
                  >
                    {emoji}
                  </Button>
                ))}
              </Grid>
            </Box>
          ))}
        </VStack>
      </Box>
    </VStack>
  );
};

export default SimpleEmojiPicker;
