/* Custom styles for emoji picker to match Telegram design */

.telegram-emoji-picker .epr-main {
  border: none !important;
  background: transparent !important;
}

.telegram-emoji-picker .epr-header {
  border-bottom: 1px solid var(--chakra-colors-gray-200) !important;
  background: var(--chakra-colors-gray-50) !important;
  padding: 8px 12px !important;
}

.telegram-emoji-picker .epr-body {
  background: transparent !important;
}

.telegram-emoji-picker .epr-emoji-category-label {
  background: var(--chakra-colors-gray-50) !important;
  color: var(--chakra-colors-gray-600) !important;
  font-size: 12px !important;
  font-weight: 600 !important;
  padding: 8px 12px !important;
  margin: 0 !important;
  border-bottom: 1px solid var(--chakra-colors-gray-100) !important;
}

.telegram-emoji-picker .epr-emoji {
  border-radius: 8px !important;
  transition: all 0.2s ease !important;
  margin: 2px !important;
}

.telegram-emoji-picker .epr-emoji:hover {
  background: var(--chakra-colors-gray-100) !important;
  transform: scale(1.1) !important;
}

.telegram-emoji-picker .epr-search-container {
  padding: 12px !important;
  border-bottom: 1px solid var(--chakra-colors-gray-200) !important;
}

.telegram-emoji-picker .epr-search {
  border: 1px solid var(--chakra-colors-gray-300) !important;
  border-radius: 20px !important;
  padding: 8px 16px !important;
  font-size: 14px !important;
  background: var(--chakra-colors-white) !important;
}

.telegram-emoji-picker .epr-search:focus {
  border-color: #00CC85 !important;
  box-shadow: 0 0 0 1px #00CC85 !important;
  outline: none !important;
}

.telegram-emoji-picker .epr-category-nav {
  border-top: 1px solid var(--chakra-colors-gray-200) !important;
  background: var(--chakra-colors-gray-50) !important;
  padding: 8px !important;
}

.telegram-emoji-picker .epr-cat-btn {
  border-radius: 8px !important;
  padding: 8px !important;
  margin: 0 2px !important;
  transition: all 0.2s ease !important;
}

.telegram-emoji-picker .epr-cat-btn:hover {
  background: var(--chakra-colors-gray-200) !important;
}

.telegram-emoji-picker .epr-cat-btn.epr-active {
  background: #00CC85 !important;
  color: white !important;
}

.telegram-emoji-picker .epr-preview {
  border-top: 1px solid var(--chakra-colors-gray-200) !important;
  background: var(--chakra-colors-gray-50) !important;
  padding: 12px !important;
}

/* Dark mode styles */
[data-theme="dark"] .telegram-emoji-picker .epr-header,
[data-theme="dark"] .telegram-emoji-picker .epr-emoji-category-label,
[data-theme="dark"] .telegram-emoji-picker .epr-category-nav,
[data-theme="dark"] .telegram-emoji-picker .epr-preview {
  background: var(--chakra-colors-gray-800) !important;
  border-color: var(--chakra-colors-gray-600) !important;
}

[data-theme="dark"] .telegram-emoji-picker .epr-emoji-category-label {
  color: var(--chakra-colors-gray-300) !important;
}

[data-theme="dark"] .telegram-emoji-picker .epr-emoji:hover {
  background: var(--chakra-colors-gray-700) !important;
}

[data-theme="dark"] .telegram-emoji-picker .epr-search {
  background: var(--chakra-colors-gray-700) !important;
  border-color: var(--chakra-colors-gray-600) !important;
  color: var(--chakra-colors-white) !important;
}

[data-theme="dark"] .telegram-emoji-picker .epr-cat-btn:hover {
  background: var(--chakra-colors-gray-600) !important;
}

/* Scrollbar styling */
.telegram-emoji-picker .epr-body::-webkit-scrollbar {
  width: 6px;
}

.telegram-emoji-picker .epr-body::-webkit-scrollbar-track {
  background: transparent;
}

.telegram-emoji-picker .epr-body::-webkit-scrollbar-thumb {
  background: var(--chakra-colors-gray-300);
  border-radius: 3px;
}

.telegram-emoji-picker .epr-body::-webkit-scrollbar-thumb:hover {
  background: var(--chakra-colors-gray-400);
}

[data-theme="dark"] .telegram-emoji-picker .epr-body::-webkit-scrollbar-thumb {
  background: var(--chakra-colors-gray-600);
}

[data-theme="dark"] .telegram-emoji-picker .epr-body::-webkit-scrollbar-thumb:hover {
  background: var(--chakra-colors-gray-500);
}

/* Animation for emoji picker appearance */
.telegram-emoji-picker {
  animation: emojiPickerSlideUp 0.2s ease-out;
}

@keyframes emojiPickerSlideUp {
  from {
    opacity: 0;
    transform: translateY(10px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Responsive design */
@media (max-width: 480px) {
  .telegram-emoji-picker {
    width: 300px !important;
    height: 350px !important;
  }
}
